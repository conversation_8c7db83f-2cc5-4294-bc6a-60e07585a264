import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/success_animation.dart';

class UserFormScreen extends StatefulWidget {
  final User? user;

  const UserFormScreen({Key? key, this.user}) : super(key: key);

  @override
  State<UserFormScreen> createState() => _UserFormScreenState();
}

class _UserFormScreenState extends State<UserFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final UserService _userService = UserService();
  final ScrollController _scrollController = ScrollController();

  // Form controllers
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _departmentController = TextEditingController();
  String? _selectedDepartment;

  // Form field keys for scrolling to errors
  final _usernameKey = GlobalKey();
  final _emailKey = GlobalKey();
  final _passwordKey = GlobalKey();
  final _confirmPasswordKey = GlobalKey();

  String _selectedRole = 'Normal User';
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // Password validation states
  String _passwordError = '';
  String _confirmPasswordError = '';
  double _passwordStrength = 0.0;
  String _passwordStrengthText = '';
  Color _passwordStrengthColor = Colors.red;

  List<String> _roles = ['Admin', 'Normal User'];
  List<String> _departments = [];
  bool _isDepartmentsLoading = true;

  bool get _isEditing => widget.user != null;

  @override
  void initState() {
    super.initState();


    // 立即设置默认部门，避免等待API调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setDefaultDepartments();
    });

    _loadInitialData().then((_) {

      if (_isEditing) {

        _populateForm();
      }
    }).catchError((error) {

    });

    // 添加密码实时验证监听器
    _passwordController.addListener(_validatePasswordRealtime);
    _confirmPasswordController.addListener(_validateConfirmPasswordRealtime);
  }

  void _setDefaultDepartments() {
    // 使用与后端一致的中文部门名称（简化为5个）
    final defaultDepartments = [
      "IT部门",
      "人力资源部",
      "财务部",
      "市场营销部",
      "研发部"
    ];

    setState(() {
      _departments = defaultDepartments;
      _isDepartmentsLoading = false;
    });

  }

  // 将中文部门名称转换为国际化显示名称
  String _getDepartmentDisplayName(String department) {
    final l10n = AppLocalizations.of(context)!;

    switch (department) {
      case "IT部门":
        return l10n.itDepartment;
      case "人力资源部":
        return l10n.hrDepartment;
      case "财务部":
        return l10n.financeDepartment;
      case "市场营销部":
        return l10n.marketingDepartment;
      case "研发部":
        return l10n.rdDepartment;
      default:
        return department; // 如果没有匹配的翻译，返回原始名称
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameController.dispose();
    _departmentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {


    try {
      final apiDepartments = await _userService.getDepartments();

      // 合并API返回的部门和当前的默认部门，去重
      final allDepartments = <String>{};
      allDepartments.addAll(_departments); // 当前的默认部门
      allDepartments.addAll(apiDepartments); // API返回的部门

      setState(() {
        _departments = allDepartments.toList()..sort();
      });
    } catch (e) {
      // API失败时保持默认部门不变
    }
  }

  void _populateForm() {
    final user = widget.user!;
    _usernameController.text = user.username;
    _emailController.text = user.email;
    _fullNameController.text = user.fullName ?? '';
    _selectedDepartment = user.department;
    _selectedRole = user.role;
  }

  // 实时密码验证
  void _validatePasswordRealtime() {
    final l10n = AppLocalizations.of(context)!;
    final password = _passwordController.text;
    setState(() {
      _passwordStrength = _calculatePasswordStrength(password);
      _passwordStrengthText = _getPasswordStrengthText(_passwordStrength);
      _passwordStrengthColor = _getPasswordStrengthColor(_passwordStrength);

      if (password.isNotEmpty && password.length < 6) {
        _passwordError = l10n.passwordMinLength;
      } else {
        _passwordError = '';
      }
    });

    // 如果确认密码已输入，也要重新验证一致性
    if (_confirmPasswordController.text.isNotEmpty) {
      _validateConfirmPasswordRealtime();
    }
  }

  // 实时确认密码验证
  void _validateConfirmPasswordRealtime() {
    final l10n = AppLocalizations.of(context)!;
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;

    setState(() {
      if (confirmPassword.isNotEmpty && confirmPassword != password) {
        _confirmPasswordError = l10n.passwordMismatch;
      } else {
        _confirmPasswordError = '';
      }
    });
  }

  // 计算密码强度
  double _calculatePasswordStrength(String password) {
    if (password.isEmpty) return 0.0;
    
    double strength = 0.0;
    
    // 长度检查
    if (password.length >= 6) strength += 0.2;
    if (password.length >= 8) strength += 0.1;
    if (password.length >= 12) strength += 0.1;
    
    // 包含小写字母
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.15;
    
    // 包含大写字母
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.15;
    
    // 包含数字
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.15;
    
    // 包含特殊字符
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.15;
    
    return strength.clamp(0.0, 1.0);
  }

  String _getPasswordStrengthText(double strength) {
    final l10n = AppLocalizations.of(context)!;
    if (strength == 0.0) return '';
    if (strength < 0.3) return l10n.passwordWeak;
    if (strength < 0.6) return l10n.passwordMedium;
    if (strength < 0.8) return l10n.passwordStrong;
    return l10n.passwordVeryStrong;
  }

  Color _getPasswordStrengthColor(double strength) {
    if (strength == 0.0) return Colors.grey;
    if (strength < 0.3) return Colors.red;
    if (strength < 0.6) return Colors.orange;
    if (strength < 0.8) return Colors.blue;
    return Colors.green;
  }

  // 滚动到第一个错误字段
  void _scrollToFirstError() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = _formKey.currentContext;
      if (context == null) return;

      // 检查各个字段的验证状态并滚动到第一个错误
      if (_validateUsername(_usernameController.text) != null) {
        _scrollToWidget(_usernameKey);
        return;
      }
      if (_validateEmail(_emailController.text) != null) {
        _scrollToWidget(_emailKey);
        return;
      }
      if (!_isEditing && _validatePassword(_passwordController.text) != null) {
        _scrollToWidget(_passwordKey);
        return;
      }
      if (!_isEditing && _validateConfirmPassword(_confirmPasswordController.text) != null) {
        _scrollToWidget(_confirmPasswordKey);
        return;
      }
    });
  }

  void _scrollToWidget(GlobalKey key) {
    final context = key.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        alignment: 0.1, // 滚动到屏幕顶部10%的位置
      );
    }
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      _scrollToFirstError();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final l10n = AppLocalizations.of(context)!;
    try {
      if (_isEditing) {
        // Update existing user - 只更新管理员可以修改的字段
        final request = UpdateUserRequest(
          username: widget.user!.username, // 保持原用户名不变
          email: _emailController.text.trim(),
          role: _selectedRole,
          fullName: widget.user!.fullName, // 保持原姓名不变
          department: _selectedDepartment,
        );

        await _userService.updateUser(widget.user!.id, request);

        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: l10n.updateSuccess,
          message: l10n.userInfoUpdated,
          onComplete: () {
            Navigator.of(context).pop(true);
          },
        );
        return;
      } else {
        // Create new user
        final request = CreateUserRequest(
          username: _usernameController.text.trim(),
          email: _selectedRole == 'Admin'
              ? '<EMAIL>' // 管理员使用系统默认邮箱
              : _emailController.text.trim(),
          password: _passwordController.text,
          role: _selectedRole,
          fullName: _selectedRole == 'Admin'
              ? null
              : (_fullNameController.text.trim().isEmpty
                  ? null
                  : _fullNameController.text.trim()),
          department: _selectedRole == 'Admin' ? null : _selectedDepartment,
        );

        await _userService.createUser(request);

        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: l10n.createSuccess,
          message: l10n.newUserCreated,
          onComplete: () {
            Navigator.of(context).pop(true);
          },
        );
        return;
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${l10n.operationFailed}: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String? _validateUsername(String? value) {
    final l10n = AppLocalizations.of(context)!;
    if (value == null || value.trim().isEmpty) {
      return l10n.usernameRequired;
    }
    if (value.trim().length < 3) {
      return l10n.usernameMinLength;
    }
    if (value.trim().length > 50) {
      return l10n.usernameMaxLength;
    }
    return null;
  }

  String? _validateEmail(String? value) {
    final l10n = AppLocalizations.of(context)!;
    if (value == null || value.trim().isEmpty) {
      return l10n.emailRequired;
    }

    final email = value.trim();

    // 检查长度
    if (email.length > 254) {
      return l10n.emailTooLong;
    }

    // 更严格的邮箱正则表达式
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    );

    if (!emailRegex.hasMatch(email)) {
      return l10n.emailInvalid;
    }

    // 检查是否包含连续的点
    if (email.contains('..')) {
      return l10n.emailFormatError;
    }

    // 检查是否以点开头或结尾
    if (email.startsWith('.') || email.endsWith('.')) {
      return l10n.emailFormatError;
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (_isEditing) return null; // Password not required for editing

    final l10n = AppLocalizations.of(context)!;
    if (value == null || value.isEmpty) {
      return l10n.passwordRequired;
    }
    if (value.length < 6) {
      return l10n.passwordMinLength;
    }
    if (value.length > 100) {
      return l10n.passwordMaxLength;
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (_isEditing) return null; // Password not required for editing

    final l10n = AppLocalizations.of(context)!;
    if (value == null || value.isEmpty) {
      return l10n.confirmPasswordRequired;
    }
    if (value != _passwordController.text) {
      return l10n.passwordMismatch;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(_isEditing ? l10n.editUser : l10n.createUser),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        shadowColor: Colors.transparent,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, size: 20),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        actions: [
          if (_isLoading)
            Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildModernBasicInfoSection(),
              const SizedBox(height: 20),
              if (!_isEditing) _buildModernPasswordSection(),
              if (!_isEditing) const SizedBox(height: 20),
              _buildModernAdditionalInfoSection(),
              const SizedBox(height: 32),
              _buildModernActionButtons(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernBasicInfoSection() {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.user,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.basicInfo,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              key: _usernameKey,
              child: _buildModernTextField(
                controller: _usernameController,
                label: l10n.usernameLabel,
                hint: _isEditing ? l10n.usernameReadonly : l10n.usernameHint,
                icon: Icons.person_rounded,
                validator: _isEditing ? null : _validateUsername,
                required: !_isEditing,
                enabled: !_isEditing, // 编辑时禁用用户名字段
              ),
            ),
            const SizedBox(height: 16),
            Container(
              key: _emailKey,
              child: _buildModernTextField(
                controller: _emailController,
                label: l10n.emailLabel,
                hint: _selectedRole == 'Admin' ? l10n.adminEmailManaged : l10n.emailHint,
                icon: Icons.email_rounded,
                keyboardType: TextInputType.emailAddress,
                validator: _selectedRole == 'Admin' ? null : _validateEmail,
                required: _selectedRole != 'Admin',
                enabled: _selectedRole != 'Admin', // 管理员角色时禁用
              ),
            ),
            const SizedBox(height: 16),
            _buildModernDropdown(),
          ],
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    bool required = false,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            if (required)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.red[500],
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          validator: validator,
          enabled: enabled && !_isLoading,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.grey[600], size: 18),
            ),
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.blue[500]!, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            filled: true,
            fillColor: enabled ? Colors.grey[50] : Colors.grey[100],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildModernDropdown() {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              l10n.roleLabel,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[500],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedRole,
            decoration: InputDecoration(
              hintText: l10n.selectRoleHint,
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: FaIcon(
                  FontAwesomeIcons.userShield,
                  color: Colors.grey[600],
                  size: 16,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            items: _roles.map((role) => DropdownMenuItem<String>(
              value: role,
              child: Text(
                role == 'Admin' ? l10n.administrator : l10n.regularUser,
                style: const TextStyle(fontSize: 14),
              ),
            )).toList(),
            onChanged: _isLoading ? null : (value) {
              setState(() {
                _selectedRole = value!;
                // 如果选择了管理员角色，清空并锁定相关字段
                if (_selectedRole == 'Admin') {
                  _emailController.clear();
                  _fullNameController.clear();
                  _selectedDepartment = null;
                }
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return l10n.roleRequired;
              }
              return null;
            },
          ),
        ),
        // 如果选择了管理员角色，显示提示信息
        if (_selectedRole == 'Admin')
          Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!, width: 1),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange[600], size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    l10n.adminAccountNote,
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildModernPasswordSection() {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.lock,
                    color: Colors.orange[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.passwordSettings,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              key: _passwordKey,
              child: _buildPasswordFieldWithStrength(),
            ),
            const SizedBox(height: 16),
            Container(
              key: _confirmPasswordKey,
              child: _buildConfirmPasswordField(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordFieldWithStrength() {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              l10n.passwordLabel,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[500],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _passwordController,
          obscureText: _obscurePassword,
          validator: _validatePassword,
          enabled: !_isLoading,
          decoration: InputDecoration(
            hintText: l10n.passwordHint,
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.lock_rounded, color: Colors.grey[600], size: 18),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                color: Colors.grey[600],
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _passwordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _passwordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _passwordError.isNotEmpty ? Colors.red[400]! : Colors.blue[500]!,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
        // 实时错误提示
        if (_passwordError.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.error_outline_rounded, color: Colors.red[400], size: 16),
              const SizedBox(width: 8),
              Text(
                _passwordError,
                style: TextStyle(
                  color: Colors.red[400],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
        // 密码强度条
        if (_passwordController.text.isNotEmpty) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                l10n.passwordStrength,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                _passwordStrengthText,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: _passwordStrengthColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: _passwordStrength,
              child: Container(
                decoration: BoxDecoration(
                  color: _passwordStrengthColor,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          _buildPasswordRequirements(),
        ],
      ],
    );
  }

  Widget _buildPasswordRequirements() {
    final l10n = AppLocalizations.of(context)!;
    final password = _passwordController.text;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.passwordRequirements,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          _buildRequirementItem(l10n.requireMinLength, password.length >= 6),
          _buildRequirementItem(l10n.requireLowercase, password.contains(RegExp(r'[a-z]'))),
          _buildRequirementItem(l10n.requireUppercase, password.contains(RegExp(r'[A-Z]'))),
          _buildRequirementItem(l10n.requireNumber, password.contains(RegExp(r'[0-9]'))),
          _buildRequirementItem(l10n.requireSpecialChar, password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String text, bool satisfied) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            satisfied ? Icons.check_circle_rounded : Icons.radio_button_unchecked_rounded,
            color: satisfied ? Colors.green : Colors.grey[400],
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: satisfied ? Colors.green : Colors.grey[600],
              fontWeight: satisfied ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmPasswordField() {
    final l10n = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              l10n.confirmPasswordLabel,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[500],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _confirmPasswordController,
          obscureText: _obscureConfirmPassword,
          validator: _validateConfirmPassword,
          enabled: !_isLoading,
          decoration: InputDecoration(
            hintText: l10n.confirmPasswordHint,
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.lock_outline_rounded, color: Colors.grey[600], size: 18),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscureConfirmPassword ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                color: Colors.grey[600],
              ),
              onPressed: () {
                setState(() {
                  _obscureConfirmPassword = !_obscureConfirmPassword;
                });
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _confirmPasswordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _confirmPasswordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _confirmPasswordError.isNotEmpty ? Colors.red[400]! : Colors.blue[500]!,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
        // 实时错误提示
        if (_confirmPasswordError.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.error_outline_rounded, color: Colors.red[400], size: 16),
              const SizedBox(width: 8),
              Text(
                _confirmPasswordError,
                style: TextStyle(
                  color: Colors.red[400],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
        // 密码一致性提示
        if (_confirmPasswordController.text.isNotEmpty && _confirmPasswordError.isEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.check_circle_rounded, color: Colors.green, size: 16),
              const SizedBox(width: 8),
              Text(
                l10n.passwordMatch,
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildModernAdditionalInfoSection() {
    final l10n = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.addressCard,
                    color: Colors.purple[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.additionalInfo,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildModernTextField(
              controller: _fullNameController,
              label: l10n.fullNameLabel,
              hint: _isEditing
                  ? l10n.fullNameReadonly
                  : _selectedRole == 'Admin'
                      ? l10n.adminNameManaged
                      : l10n.fullNameHint,
              icon: Icons.badge_rounded,
              validator: (_isEditing || _selectedRole == 'Admin') ? null : (value) {
                if (value != null && value.length > 100) {
                  return l10n.fullNameMaxLength;
                }
                return null;
              },
              enabled: !_isEditing && _selectedRole != 'Admin', // 编辑时或管理员角色时禁用
            ),
            const SizedBox(height: 16),
            _buildModernDepartmentField(),
          ],
        ),
      ),
    );
  }

  Widget _buildModernDepartmentField() {
    final l10n = AppLocalizations.of(context)!;



    // 如果还在加载部门数据，显示加载状态
    if (_isDepartmentsLoading) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.departmentLabel,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Loading departments...',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      );
    }

    // 确保当前选择的部门在列表中存在
    List<String> availableDepartments = List.from(_departments);

    // 如果当前选择的部门不在列表中，添加它
    if (_selectedDepartment != null &&
        _selectedDepartment!.isNotEmpty &&
        !availableDepartments.contains(_selectedDepartment)) {
      availableDepartments.add(_selectedDepartment!);
    }

    // 去重并排序
    availableDepartments = availableDepartments.toSet().toList();
    availableDepartments.sort();



    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.departmentLabel,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: _selectedRole == 'Admin' ? Colors.grey[100] : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedDepartment,
            isExpanded: true, // 防止溢出
            decoration: InputDecoration(
              hintText: _selectedRole == 'Admin' ? l10n.adminDepartmentManaged : l10n.selectDepartmentHint,
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.business_rounded,
                  color: Colors.purple[600],
                  size: 18,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            items: [
              DropdownMenuItem<String>(
                value: null,
                child: Text(
                  l10n.selectDepartmentHint,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              ...availableDepartments.map((dept) => DropdownMenuItem<String>(
                value: dept, // 保存原始中文名称
                child: Text(
                  _getDepartmentDisplayName(dept), // 显示国际化名称
                  overflow: TextOverflow.ellipsis,
                ),
              )),
            ],
            onChanged: (!_isLoading && _selectedRole != 'Admin') ? (value) {
              setState(() {
                _selectedDepartment = value;
              });
            } : null,
            icon: Icon(
              Icons.arrow_drop_down_rounded,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernActionButtons() {
    final l10n = AppLocalizations.of(context)!;
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: TextButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.close_rounded, size: 18, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    l10n.cancel,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue[600]!, Colors.blue[700]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveUser,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isEditing ? l10n.updating : l10n.creating,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _isEditing ? Icons.update_rounded : Icons.person_add_rounded,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isEditing ? l10n.updateUser : l10n.createUser,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ],
    );
  }
} 