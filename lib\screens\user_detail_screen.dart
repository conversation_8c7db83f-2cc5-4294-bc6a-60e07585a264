import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../widgets/success_animation.dart';
import 'user_form_screen.dart';
import '../utils/timezone_utils.dart';

class UserDetailScreen extends StatefulWidget {
  final User user;

  const UserDetailScreen({Key? key, required this.user}) : super(key: key);

  @override
  State<UserDetailScreen> createState() => _UserDetailScreenState();
}

class _UserDetailScreenState extends State<UserDetailScreen> {
  final UserService _userService = UserService();
  late User _user;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _user = widget.user;
  }

  Future<void> _refreshUser() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final updatedUser = await _userService.getUserById(_user.id);
      setState(() {
        _user = updatedUser;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${AppLocalizations.of(context)!.refreshFailed}: $e')),
      );
    }
  }

  Future<void> _deleteUser() async {
    final l10n = AppLocalizations.of(context)!;
    final confirmed = await _showModernConfirmDialog(
      title: l10n.confirmDelete,
      content: l10n.confirmDeleteUser(_user.username),
      confirmText: l10n.deleteUser,
      confirmColor: Colors.red,
      icon: Icons.delete_forever_rounded,
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _userService.deleteUser(_user.id);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: l10n.deleteSuccess,
          message: l10n.userDeletedSuccess(_user.username),
          onComplete: () {
            Navigator.of(context).pop(true);
          },
        );
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.deleteFailed}: $e')),
        );
      }
    }
  }

  void _navigateToEditUser() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserFormScreen(user: _user),
      ),
    );

    if (result == true) {
      _refreshUser();
    }
  }

  void _showChangePasswordDialog() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    bool obscureCurrentPassword = true;
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;
    bool isLoading = false;
    
    // 密码验证状态
    String currentPasswordError = '';
    String newPasswordError = '';
    String confirmPasswordError = '';
    double passwordStrength = 0.0;
    String passwordStrengthText = '';
    Color passwordStrengthColor = Colors.red;

    // 计算密码强度
    double calculatePasswordStrength(String password) {
      if (password.isEmpty) return 0.0;
      
      double strength = 0.0;
      
      // 长度检查
      if (password.length >= 6) strength += 0.2;
      if (password.length >= 8) strength += 0.1;
      if (password.length >= 12) strength += 0.1;
      
      // 包含小写字母
      if (password.contains(RegExp(r'[a-z]'))) strength += 0.15;
      
      // 包含大写字母
      if (password.contains(RegExp(r'[A-Z]'))) strength += 0.15;
      
      // 包含数字
      if (password.contains(RegExp(r'[0-9]'))) strength += 0.15;
      
      // 包含特殊字符
      if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.15;
      
      return strength.clamp(0.0, 1.0);
    }

    String getPasswordStrengthText(double strength) {
      final l10n = AppLocalizations.of(context)!;
      if (strength == 0.0) return '';
      if (strength < 0.3) return l10n.passwordWeak;
      if (strength < 0.6) return l10n.passwordMedium;
      if (strength < 0.8) return l10n.passwordStrong;
      return l10n.passwordVeryStrong;
    }

    Color getPasswordStrengthColor(double strength) {
      if (strength == 0.0) return Colors.grey;
      if (strength < 0.3) return Colors.red;
      if (strength < 0.6) return Colors.orange;
      if (strength < 0.8) return Colors.blue;
      return Colors.green;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {

          return Dialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题栏
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.orange[50],
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: FaIcon(
                            FontAwesomeIcons.lock,
                            color: Colors.orange[600],
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          AppLocalizations.of(context)!.changePassword,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close_rounded),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    // 当前密码
                    _buildPasswordFieldInDialog(
                      controller: currentPasswordController,
                      label: AppLocalizations.of(context)!.currentPasswordLabel,
                      hint: AppLocalizations.of(context)!.currentPasswordHint,
                      obscureText: obscureCurrentPassword,
                      onToggleVisibility: () {
                        setDialogState(() {
                          obscureCurrentPassword = !obscureCurrentPassword;
                        });
                      },
                      enabled: !isLoading,
                      errorText: currentPasswordError,
                      onChanged: (value) {
                        // 清除之前的错误信息
                        if (currentPasswordError.isNotEmpty) {
                          setDialogState(() {
                            currentPasswordError = '';
                          });
                        }
                        
                        // 如果新密码已输入，检查是否与当前密码相同
                        final l10n = AppLocalizations.of(context)!;
                        if (newPasswordController.text.isNotEmpty &&
                            newPasswordController.text == value) {
                          setDialogState(() {
                            newPasswordError = l10n.newPasswordSameAsCurrent;
                          });
                        } else if (newPasswordError == l10n.newPasswordSameAsCurrent) {
                          setDialogState(() {
                            newPasswordError = '';
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    // 新密码
                    _buildPasswordFieldInDialog(
                      controller: newPasswordController,
                      label: AppLocalizations.of(context)!.newPasswordLabel,
                      hint: AppLocalizations.of(context)!.newPasswordHint,
                      obscureText: obscureNewPassword,
                      onToggleVisibility: () {
                        setDialogState(() {
                          obscureNewPassword = !obscureNewPassword;
                        });
                      },
                      enabled: !isLoading,
                      errorText: newPasswordError,
                      onChanged: (value) {
                        setDialogState(() {
                          passwordStrength = calculatePasswordStrength(value);
                          passwordStrengthText = getPasswordStrengthText(passwordStrength);
                          passwordStrengthColor = getPasswordStrengthColor(passwordStrength);
                          
                          final l10n = AppLocalizations.of(context)!;
                          if (value.isNotEmpty && value.length < 6) {
                            newPasswordError = l10n.newPasswordMinLength;
                          } else if (value.isNotEmpty && value == currentPasswordController.text) {
                            newPasswordError = l10n.newPasswordSameAsCurrent;
                          } else {
                            newPasswordError = '';
                          }
                        });
                        
                        // 如果确认密码已输入，也要重新验证一致性
                        if (confirmPasswordController.text.isNotEmpty) {
                          setDialogState(() {
                            final l10n = AppLocalizations.of(context)!;
                            if (confirmPasswordController.text != value) {
                              confirmPasswordError = l10n.newPasswordMismatch;
                            } else {
                              confirmPasswordError = '';
                            }
                          });
                        }
                      },
                    ),
                    // 密码强度条
                    if (newPasswordController.text.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Text(
                            AppLocalizations.of(context)!.passwordStrength,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            passwordStrengthText,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              color: passwordStrengthColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 6,
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: passwordStrength,
                          child: Container(
                            decoration: BoxDecoration(
                              color: passwordStrengthColor,
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildPasswordRequirementsInDialog(newPasswordController.text),
                    ],
                    const SizedBox(height: 16),
                    // 确认新密码
                    _buildPasswordFieldInDialog(
                      controller: confirmPasswordController,
                      label: AppLocalizations.of(context)!.confirmNewPasswordLabel,
                      hint: AppLocalizations.of(context)!.confirmNewPasswordHint,
                      obscureText: obscureConfirmPassword,
                      onToggleVisibility: () {
                        setDialogState(() {
                          obscureConfirmPassword = !obscureConfirmPassword;
                        });
                      },
                      enabled: !isLoading,
                      errorText: confirmPasswordError,
                      onChanged: (value) {
                        setDialogState(() {
                          final l10n = AppLocalizations.of(context)!;
                          if (value.isNotEmpty && value != newPasswordController.text) {
                            confirmPasswordError = l10n.newPasswordMismatch;
                          } else {
                            confirmPasswordError = '';
                          }
                        });
                      },
                    ),
                    // 密码一致性提示
                    if (confirmPasswordController.text.isNotEmpty && confirmPasswordError.isEmpty) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.check_circle_rounded, color: Colors.green, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            AppLocalizations.of(context)!.passwordMatch,
                            style: const TextStyle(
                              color: Colors.green,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                    const SizedBox(height: 32),
                    // 操作按钮
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[300]!, width: 1),
                            ),
                            child: TextButton(
                              onPressed: isLoading ? null : () => Navigator.pop(context),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Text(
                                AppLocalizations.of(context)!.cancel,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Colors.orange[600]!, Colors.orange[700]!],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withOpacity(0.3),
                                  spreadRadius: 0,
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed: isLoading ? null : () async {
                                // 验证所有字段
                                setDialogState(() {
                                  currentPasswordError = '';
                                  newPasswordError = '';
                                  confirmPasswordError = '';
                                });

                                final l10n = AppLocalizations.of(context)!;
                                if (currentPasswordController.text.isEmpty) {
                                  setDialogState(() {
                                    currentPasswordError = l10n.currentPasswordRequired;
                                  });
                                  return;
                                }

                                if (newPasswordController.text.isEmpty) {
                                  setDialogState(() {
                                    newPasswordError = l10n.newPasswordRequired;
                                  });
                                  return;
                                }

                                if (newPasswordController.text.length < 6) {
                                  setDialogState(() {
                                    newPasswordError = l10n.newPasswordMinLength;
                                  });
                                  return;
                                }

                                if (newPasswordController.text == currentPasswordController.text) {
                                  setDialogState(() {
                                    newPasswordError = l10n.newPasswordSameAsCurrent;
                                  });
                                  return;
                                }

                                if (confirmPasswordController.text.isEmpty) {
                                  setDialogState(() {
                                    confirmPasswordError = l10n.confirmNewPasswordRequired;
                                  });
                                  return;
                                }

                                if (newPasswordController.text != confirmPasswordController.text) {
                                  setDialogState(() {
                                    confirmPasswordError = l10n.newPasswordMismatch;
                                  });
                                  return;
                                }

                                setDialogState(() {
                                  isLoading = true;
                                });

                                try {
                                  final request = ChangePasswordRequest(
                                    currentPassword: currentPasswordController.text,
                                    newPassword: newPasswordController.text,
                                  );

                                  await _userService.changePassword(_user.id, request);
                                  Navigator.of(context).pop();
                                  
                                  // 显示现代化的成功动画
                                  SuccessAnimationOverlay.show(
                                    context,
                                    title: l10n.passwordChangeSuccess,
                                    message: l10n.passwordUpdated,
                                    onComplete: () {
                                      // 成功动画完成后的回调
                                    },
                                  );
                                } catch (e) {
                                  setDialogState(() {
                                    isLoading = false;
                                    // 根据错误类型设置相应的错误信息
                                    String errorMessage = e.toString().toLowerCase();
                                    if (errorMessage.contains('current password') ||
                                        errorMessage.contains('当前密码') ||
                                        errorMessage.contains('incorrect') ||
                                        errorMessage.contains('wrong') ||
                                        errorMessage.contains('invalid') ||
                                        errorMessage.contains('400')) {
                                      currentPasswordError = l10n.currentPasswordIncorrect;
                                    } else if (errorMessage.contains('network') ||
                                               errorMessage.contains('connection') ||
                                               errorMessage.contains('timeout')) {
                                      currentPasswordError = l10n.networkConnectionFailed;
                                    } else {
                                      currentPasswordError = l10n.passwordChangeFailed;
                                    }
                                  });
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                foregroundColor: Colors.white,
                                elevation: 0,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: isLoading
                                  ? Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          width: 18,
                                          height: 18,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          AppLocalizations.of(context)!.changingPassword,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        const Icon(Icons.lock_reset_rounded, size: 18),
                                        const SizedBox(width: 8),
                                        Text(
                                          AppLocalizations.of(context)!.confirmChange,
                                          style: const TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPasswordFieldInDialog({
    required TextEditingController controller,
    required String label,
    required String hint,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    required bool enabled,
    required String errorText,
    void Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: errorText.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: TextFormField(
            controller: controller,
            obscureText: obscureText,
            enabled: enabled,
            onChanged: onChanged,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.lock_rounded, color: Colors.grey[600], size: 18),
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  obscureText ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                  color: Colors.grey[600],
                ),
                onPressed: onToggleVisibility,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
          ),
        ),
        // 实时错误提示
        if (errorText.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.error_outline_rounded, color: Colors.red[400], size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  errorText,
                  style: TextStyle(
                    color: Colors.red[400],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildPasswordRequirementsInDialog(String password) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.passwordRequirements,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          _buildRequirementItemInDialog(AppLocalizations.of(context)!.requireMinLength, password.length >= 6),
          _buildRequirementItemInDialog(AppLocalizations.of(context)!.requireLowercase, password.contains(RegExp(r'[a-z]'))),
          _buildRequirementItemInDialog(AppLocalizations.of(context)!.requireUppercase, password.contains(RegExp(r'[A-Z]'))),
          _buildRequirementItemInDialog(AppLocalizations.of(context)!.requireNumber, password.contains(RegExp(r'[0-9]'))),
          _buildRequirementItemInDialog(AppLocalizations.of(context)!.requireSpecialChar, password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))),
        ],
      ),
    );
  }

  Widget _buildRequirementItemInDialog(String text, bool satisfied) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            satisfied ? Icons.check_circle_rounded : Icons.radio_button_unchecked_rounded,
            color: satisfied ? Colors.green : Colors.grey[400],
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: satisfied ? Colors.green : Colors.grey[600],
              fontWeight: satisfied ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<bool?> _showModernConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required Color confirmColor,
    required IconData icon,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: confirmColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(32),
                ),
                child: Icon(icon, color: confirmColor, size: 32),
              ),
              const SizedBox(height: 20),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                content,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[300]!, width: 1),
                      ),
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.cancel,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: confirmColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: confirmColor.withOpacity(0.3),
                            spreadRadius: 0,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          confirmText,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(_user.fullName ?? _user.username),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        shadowColor: Colors.transparent,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, size: 20),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(Icons.refresh_rounded, color: Colors.blue[600], size: 20),
              onPressed: _refreshUser,
              tooltip: AppLocalizations.of(context)!.refreshTooltip,
            ),
          ),
          // 如果查看的用户是管理员，隐藏所有操作按钮
          if (_user.role != UserRole.admin)
            Container(
              margin: const EdgeInsets.only(right: 8, top: 8, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: PopupMenuButton<String>(
                icon: Icon(Icons.more_vert_rounded, color: Colors.grey[600], size: 20),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _navigateToEditUser();
                      break;
                    case 'change_password':
                      _showChangePasswordDialog();
                      break;
                    case 'delete':
                      _deleteUser();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        const Icon(Icons.edit_rounded, size: 18),
                        const SizedBox(width: 12),
                        Text(AppLocalizations.of(context)!.editUserAction),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'change_password',
                    child: Row(
                      children: [
                        const Icon(Icons.lock_reset_rounded, size: 18),
                        const SizedBox(width: 12),
                        Text(AppLocalizations.of(context)!.changePasswordAction),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        const Icon(Icons.delete_rounded, size: 18, color: Colors.red),
                        const SizedBox(width: 12),
                        Text(AppLocalizations.of(context)!.deleteUserAction, style: const TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 64,
                      height: 64,
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(32),
                      ),
                      child: Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                          strokeWidth: 3,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      AppLocalizations.of(context)!.loadingUserInfo,
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildModernUserHeader(),
                  const SizedBox(height: 20),
                  _buildModernBasicInfoCard(),
                  const SizedBox(height: 16),
                  _buildModernAdditionalInfoCard(),
                  const SizedBox(height: 16),
                  _buildModernStatsCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildModernUserHeader() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            // 现代化头像
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: _user.avatarUrl != null && _user.avatarUrl!.isNotEmpty
                    ? null
                    : LinearGradient(
                        colors: _user.isActive
                            ? [_getRoleColor(_user.role).withOpacity(0.8), _getRoleColor(_user.role)]
                            : [Colors.grey[400]!, Colors.grey[600]!],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                boxShadow: [
                  BoxShadow(
                    color: (_user.isActive ? _getRoleColor(_user.role) : Colors.grey).withOpacity(0.3),
                    spreadRadius: 0,
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: _user.avatarUrl != null && _user.avatarUrl!.isNotEmpty
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Image.network(
                        _getFullAvatarUrl(_user.avatarUrl!),
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // 如果头像加载失败，显示角色图标
                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              gradient: LinearGradient(
                                colors: _user.isActive
                                    ? [_getRoleColor(_user.role).withOpacity(0.8), _getRoleColor(_user.role)]
                                    : [Colors.grey[400]!, Colors.grey[600]!],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                            ),
                            child: Center(
                              child: _user.role == UserRole.admin
                                  ? const FaIcon(
                                      FontAwesomeIcons.userShield,
                                      color: Colors.white,
                                      size: 32,
                                    )
                                  : const FaIcon(
                                      FontAwesomeIcons.user,
                                      color: Colors.white,
                                      size: 28,
                                    ),
                            ),
                          );
                        },
                      ),
                    )
                  : Center(
                      child: _user.role == UserRole.admin
                          ? const FaIcon(
                              FontAwesomeIcons.userShield,
                              color: Colors.white,
                              size: 32,
                            )
                          : const FaIcon(
                              FontAwesomeIcons.user,
                              color: Colors.white,
                              size: 28,
                            ),
                    ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          _user.fullName ?? _user.username,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w800,
                            color: _user.isActive ? Colors.black87 : Colors.grey[600],
                            letterSpacing: -0.5,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (!_user.isActive)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.red[200]!, width: 1),
                          ),
                          child: Text(
                            AppLocalizations.of(context)!.userBannedStatus,
                            style: TextStyle(
                              color: Colors.red[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _user.email,
                    style: TextStyle(
                      fontSize: 16,
                      color: _user.isActive ? Colors.grey[600] : Colors.grey[500],
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: _getRoleColor(_user.role).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: _getRoleColor(_user.role).withOpacity(0.3), width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FaIcon(
                          _user.role == UserRole.admin 
                              ? FontAwesomeIcons.crown 
                              : FontAwesomeIcons.user,
                          color: _getRoleColor(_user.role),
                          size: 14,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _user.role == UserRole.admin
                              ? AppLocalizations.of(context)!.administrator
                              : AppLocalizations.of(context)!.regularUser,
                          style: TextStyle(
                            color: _getRoleColor(_user.role),
                            fontWeight: FontWeight.w700,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernBasicInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.idCard,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocalizations.of(context)!.basicInfo,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildModernInfoRow(Icons.person_rounded, AppLocalizations.of(context)!.usernameLabel, _user.username),
            _buildModernInfoRow(Icons.email_rounded, AppLocalizations.of(context)!.emailLabel, _user.email),
            _buildModernInfoRow(Icons.admin_panel_settings_rounded, AppLocalizations.of(context)!.roleLabel,
                _user.role == UserRole.admin ? AppLocalizations.of(context)!.administrator : AppLocalizations.of(context)!.regularUser),
            if (_user.fullName != null && _user.fullName!.isNotEmpty)
              _buildModernInfoRow(Icons.badge_rounded, AppLocalizations.of(context)!.fullNameLabel, _user.fullName!),
            if (_user.department != null && _user.department!.isNotEmpty)
              _buildModernInfoRow(Icons.business_rounded, AppLocalizations.of(context)!.departmentLabel, _user.department!),
          ],
        ),
      ),
    );
  }

  Widget _buildModernAdditionalInfoCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.clock,
                    color: Colors.green[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocalizations.of(context)!.timeInfo,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildModernInfoRow(
              Icons.access_time_rounded,
              AppLocalizations.of(context)!.createdTime,
              TimezoneUtils.formatWithPattern(_user.createdAt, 'yyyy-MM-dd HH:mm:ss'),
            ),
            _buildModernInfoRow(
              Icons.update_rounded,
              AppLocalizations.of(context)!.updatedTime,
              TimezoneUtils.formatWithPattern(_user.updatedAt, 'yyyy-MM-dd HH:mm:ss'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernStatsCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.chartBar,
                    color: Colors.purple[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocalizations.of(context)!.statisticsInfo,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildModernStatItem(
                    FontAwesomeIcons.laptop,
                    AppLocalizations.of(context)!.assignedAssets,
                    _user.assignedAssetsCount.toString(),
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildModernStatItem(
                    _user.isActive ? FontAwesomeIcons.checkCircle : FontAwesomeIcons.ban,
                    AppLocalizations.of(context)!.accountStatus,
                    _user.isActive ? AppLocalizations.of(context)!.statusNormal : AppLocalizations.of(context)!.statusBanned,
                    _user.isActive ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 18, color: Colors.grey[600]),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernStatItem(IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: FaIcon(icon, size: 24, color: color),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w800,
              color: color,
              letterSpacing: -0.5,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 获取完整的头像URL
  String _getFullAvatarUrl(String avatarUrl) {
    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      return avatarUrl;
    } else {
      // 相对URL，需要添加基础URL
      return 'http://10.0.2.2:5000$avatarUrl';
    }
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'normal user':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
} 