{"@@locale": "en", "appTitle": "IT Asset Manager", "appSubtitle": "Asset Management System", "welcomeBack": "Welcome back, ", "pleaseLoginToYourAccount": "Please login to your account", "username": "Username", "usernameHint": "Please enter username", "password": "Password", "passwordHint": "Please enter password (at least 6 characters)", "login": "<PERSON><PERSON>", "loggingIn": "Logging in...", "loginSuccess": "Login Successful", "welcomeBackMessage": "Welcome back!", "secureLogin": "<PERSON><PERSON>", "encryptedTransmission": "Encrypted Data Transmission", "version": "IT Asset Manager v2.0", "pleaseEnterUsername": "Please enter username", "pleaseEnterPassword": "Please enter password", "passwordMinLength": "Password must be at least 6 characters", "userNotExists": "User does not exist, please check username", "passwordIncorrect": "Password is incorrect, please re-enter", "accountDisabled": "Account has been disabled, please contact administrator", "loginFailed": "<PERSON><PERSON> failed", "networkTimeout": "Network connection timeout, please check network connection", "serverError": "Server error, please try again later", "networkError": "Network error: Please check your network connection", "requestCancelled": "Request cancelled", "unknownError": "Unknown error", "switchLanguage": "中文", "@switchLanguage": {"description": "Text shown on language switch button to switch to Chinese"}, "userNotLoggedIn": "User not logged in, please log in again", "confirmDelete": "Confirm Delete", "confirmDeleteUser": "Are you sure you want to delete user \"{username}\"? This action cannot be undone.", "@confirmDeleteUser": {"placeholders": {"username": {"type": "String"}}}, "deleteSuccess": "Delete Successful", "userDeletedSuccess": "User has been successfully deleted", "@userDeletedSuccess": {"placeholders": {"username": {"type": "String"}}}, "deleteFailed": "Delete failed: {error}", "changeUserRole": "Change User Role - {username}", "@changeUserRole": {"placeholders": {"username": {"type": "String"}}}, "currentRole": "Current Role: {role}", "@currentRole": {"placeholders": {"role": {"type": "String"}}}, "selectNewRole": "Select New Role", "roleChangeSuccess": "Role Change Successful", "userRoleChanged": "User \"{username}\" role has been changed to {newRole}", "@userRoleChanged": {"placeholders": {"username": {"type": "String"}, "newRole": {"type": "String"}}}, "roleChangeFailed": "Role Change Failed", "loginExpired": "<PERSON>gin expired, please log in again", "unauthorizedRoleChange": "Insufficient permissions: Only administrators can modify user roles", "banUser": "Ban User", "unbanUser": "Unban User", "confirmBan": "Confirm Ban", "confirmUnban": "Confirm <PERSON>ban", "confirmBanUser": "Are you sure you want to ban user \"{username}\"?", "@confirmBanUser": {"placeholders": {"username": {"type": "String"}}}, "confirmUnbanUser": "Are you sure you want to unban user \"{username}\"?", "@confirmUnbanUser": {"placeholders": {"username": {"type": "String"}}}, "banSuccess": "Ban Successful", "unbanSuccess": "Unban Successful", "userBanned": "Banned", "@userBanned": {"placeholders": {"username": {"type": "String"}}}, "userUnbanned": "User \"{username}\" has been unbanned", "@userUnbanned": {"placeholders": {"username": {"type": "String"}}}, "banFailed": "Ban Failed", "unbanFailed": "Unban Failed", "unauthorizedBan": "Insufficient permissions: Only administrators can ban users", "unauthorizedUnban": "Insufficient permissions: Only administrators can unban users", "noUsersToExport": "No user data to export", "exportingExcel": "Exporting Excel file...", "exportSuccess": "Export Successful", "excelSavedTo": "Excel file saved to: {filePath}", "@excelSavedTo": {"placeholders": {"filePath": {"type": "String"}}}, "exportFailed": "Export failed: {error}", "selectSaveLocation": "Select Save Location", "selectExcelSaveLocation": "Please select the save location for Excel file:", "appDirectory": "App Directory", "downloadFolder": "Download Folder", "totalRecords": "Total {count} records", "@totalRecords": {"placeholders": {"count": {"type": "int"}}}, "clearFilters": "Clear", "searchUsersHint": "Search user ID, username, email...", "filterTooltip": "Filter", "exportExcelTooltip": "Export Excel", "addUserTooltip": "Add User", "filterConditions": "Filter Conditions", "allRoles": "All Roles", "administrator": "Administrator", "regularUser": "Regular User", "allDepartments": "All Departments", "resetFilters": "Reset", "applyFilters": "Apply Filters", "loadingUsers": "Loading user data...", "loadFailed": "Load Failed", "relogin": "Re-login", "reload": "Reload", "noUsersFound": "No matching users found", "noUsersData": "No user data available", "adjustFilters": "Try adjusting search or filter conditions", "addFirstUser": "Click the button above to add the first user", "changeRole": "Change Role", "deleteUser": "Delete", "roleLabel": "Role", "departmentLabel": "Department", "assetsCount": "Assets", "createdAt": "Created on {date}", "@createdAt": {"placeholders": {"date": {"type": "String"}}}, "itDepartment": "IT Department", "financeDepartment": "Finance Department", "hrDepartment": "HR Department", "marketingDepartment": "Marketing Department", "salesDepartment": "Sales Department", "operationsDepartment": "Operations Department", "customerServiceDepartment": "Customer Service Department", "rdDepartment": "R&D Department", "administrationDepartment": "Administration Department", "legalDepartment": "Legal Department", "productDepartment": "Product Department", "trainingDepartment": "Training Department", "securityDepartment": "Security Department", "qualityDepartment": "Quality Management Department", "procurementDepartment": "Procurement Department", "editUser": "Edit User", "createUser": "Create User", "basicInfo": "Basic Information", "usernameLabel": "Username", "usernameReadonly": "<PERSON><PERSON><PERSON> (Read-only)", "emailLabel": "Email Address", "emailHint": "Please enter email address", "adminEmailManaged": "Administrator email is managed by the system", "selectRoleHint": "Please select role", "adminAccountNote": "Administrator account's email, name and department will be automatically managed by the system", "passwordSettings": "Password Settings", "passwordLabel": "Password", "passwordStrength": "Password Strength: ", "passwordWeak": "Weak", "passwordMedium": "Medium", "passwordStrong": "Strong", "passwordVeryStrong": "Very Strong", "passwordRequirements": "Password Requirements:", "requireMinLength": "At least 6 characters", "requireLowercase": "Contains lowercase letters", "requireUppercase": "Contains uppercase letters", "requireNumber": "Contains numbers", "requireSpecialChar": "Contains special characters", "confirmPasswordLabel": "Confirm Password", "confirmPasswordHint": "Please enter password again", "passwordMatch": "Passwords match", "additionalInfo": "Additional Information", "fullNameLabel": "Full Name", "fullNameHint": "Please enter full name", "fullNameReadonly": "Full Name (Read-only)", "adminNameManaged": "Administrator name is managed by the system", "selectDepartmentHint": "Please select department", "adminDepartmentManaged": "Administrator department is managed by the system", "updating": "Updating...", "creating": "Creating...", "updateUser": "Update User", "updateSuccess": "Update Successful", "userInfoUpdated": "User information has been successfully updated", "createSuccess": "Create Successful", "newUserCreated": "New user has been successfully created", "operationFailed": "Operation failed: {error}", "usernameRequired": "Please enter username", "usernameMinLength": "Username must be at least 3 characters", "usernameMaxLength": "Username cannot exceed 50 characters", "emailRequired": "Please enter email address", "emailTooLong": "Email address is too long", "emailInvalid": "Please enter a valid email address", "emailFormatError": "Email address format is incorrect", "passwordRequired": "Please enter password", "passwordMaxLength": "Password cannot exceed 100 characters", "confirmPasswordRequired": "Please confirm password", "passwordMismatch": "Passwords do not match", "roleRequired": "Please select role", "fullNameMaxLength": "Full name cannot exceed 100 characters", "refreshFailed": "Refresh Failed", "changePassword": "Change Password", "currentPasswordLabel": "Current Password", "currentPasswordHint": "Please enter current password", "newPasswordLabel": "New Password", "newPasswordHint": "Please enter new password (at least 6 characters)", "confirmNewPasswordLabel": "Confirm New Password", "confirmNewPasswordHint": "Please enter new password again", "passwordChangeSuccess": "Password Change Successful", "passwordUpdated": "Your password has been successfully updated", "currentPasswordIncorrect": "Current password is incorrect", "networkConnectionFailed": "Network connection failed, please try again", "passwordChangeFailed": "Password change failed, please try again", "changingPassword": "Changing...", "confirmChange": "Confirm Change", "currentPasswordRequired": "Please enter current password", "newPasswordRequired": "Please enter new password", "newPasswordMinLength": "New password must be at least 6 characters", "newPasswordSameAsCurrent": "New password cannot be the same as current password", "confirmNewPasswordRequired": "Please confirm new password", "newPasswordMismatch": "New passwords do not match", "refreshTooltip": "Refresh", "editUserAction": "Edit User", "changePasswordAction": "Change Password", "deleteUserAction": "Delete User", "loadingUserInfo": "Loading user information...", "userBannedStatus": "Banned", "timeInfo": "Time Information", "createdTime": "Created Time", "updatedTime": "Updated Time", "statisticsInfo": "Statistics Information", "assignedAssets": "Assigned Assets", "accountStatus": "Account Status", "statusNormal": "Normal", "statusBanned": "Banned", "activityLogTitle": "Activity Log", "searchActivityHint": "Search activity ID, title keywords...", "activityType": "Activity Type", "dateRange": "Date Range", "startDate": "Start Date", "endDate": "End Date", "allTypes": "All Types", "applyFilter": "Apply Filter", "clearFilter": "Clear Filter", "exportExcel": "Export Excel", "exportTooltip": "Export Excel", "activityTypeCreate": "Create", "activityTypeUpdate": "Update", "activityTypeDelete": "Delete", "activityTypeAssign": "Assign", "activityTypeUnassign": "Unassign", "activityTypeMaintenance": "Maintenance", "activityTypeDispose": "Dispose", "loadingActivityLogs": "Loading activity logs...", "loadActivityLogsFailed": "Failed to load activity logs", "noActivityLogs": "No activity logs available", "noActivityLogsFound": "No matching activity logs found", "activityLogRecordCount": "{count} records found", "@activityLogRecordCount": {"placeholders": {"count": {"type": "int"}}}, "exportingExcelFile": "Exporting Excel file...", "exportSuccessTitle": "Export Successful", "exportSuccessMessage": "Excel file saved to: {path}", "@exportSuccessMessage": {"placeholders": {"path": {"type": "String"}}}, "exportFailedMessage": "Export failed: {error}", "@exportFailedMessage": {"placeholders": {"error": {"type": "String"}}}, "selectSaveLocationTitle": "Select Save Location", "selectSaveLocationMessage": "Please select the save location for Excel file:", "appDirectoryOption": "App Directory", "downloadFolderOption": "Download Folder", "assetAssignedTo": "{asset} assigned to {user}", "@assetAssignedTo": {"placeholders": {"asset": {"type": "String"}, "user": {"type": "String"}}}, "assetUnassignedFrom": "{asset} unassigned from {user}", "@assetUnassignedFrom": {"placeholders": {"asset": {"type": "String"}, "user": {"type": "String"}}}, "assetSetToMaintenance": "{asset} set to maintenance", "@assetSetToMaintenance": {"placeholders": {"asset": {"type": "String"}}}, "assetDisposed": "{asset} disposed", "@assetDisposed": {"placeholders": {"asset": {"type": "String"}}}, "assetSetToMaintenanceStatus": "{asset} set to maintenance status", "@assetSetToMaintenanceStatus": {"placeholders": {"asset": {"type": "String"}}}, "assetSetToRetiredStatus": "{asset} set to retired status", "@assetSetToRetiredStatus": {"placeholders": {"asset": {"type": "String"}}}, "totalRecordsFound": "{count} records found", "@totalRecordsFound": {"placeholders": {"count": {"type": "int"}}}, "assetInfo": "Asset Information", "activityTime": "Activity Time", "operator": "Operator", "activityDescription": "Activity Description", "changeDetails": "Change Details", "fieldName": "Field", "oldValue": "Old Value", "newValue": "New Value", "noChangeDetails": "No change details", "viewDetails": "View Details", "hideDetails": "Hide Details", "assetCreated": "Created asset {asset}", "@assetCreated": {"placeholders": {"asset": {"type": "String"}}}, "assetUpdated": "Updated asset {asset}", "@assetUpdated": {"placeholders": {"asset": {"type": "String"}}}, "assetDeleted": "Deleted asset {asset}", "@assetDeleted": {"placeholders": {"asset": {"type": "String"}}}, "assetSetToStatus": "Asset {asset} set to {status}", "@assetSetToStatus": {"placeholders": {"asset": {"type": "String"}, "status": {"type": "String"}}}, "statusAvailable": "Available", "statusAssigned": "Assigned", "statusMaintenance": "Maintenance", "statusRetired": "Retired", "categoryLaptop": "Laptop", "categoryDesktop": "Desktop", "categoryMonitor": "Monitor", "categoryPrinter": "Printer", "categoryPhone": "Phone", "categoryTablet": "Tablet", "categoryServer": "Server", "categoryNetwork": "Network", "categoryOther": "Other", "noValue": "None", "asset": "<PERSON><PERSON>", "retry": "Retry", "previousPage": "Previous", "nextPage": "Next", "pageInfo": "Page {current} of {total}", "@pageInfo": {"placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "validationError": "Validation error", "requestError": "Request error", "permissionDenied": "Permission denied, please contact administrator", "resourceNotFound": "The requested resource does not exist", "dataConflict": "Data conflict, please refresh and try again", "dataValidationFailed": "Data validation failed", "serviceUnavailable": "Service temporarily unavailable, please try again later", "requestFailed": "Request failed, please try again", "dashboard": "Dashboard", "assetManagement": "Assets", "ticketManagement": "Tickets", "activityLog": "Activity", "userManagement": "Users", "createAsset": "Create Asset", "editAsset": "Edit Asset", "assetDetails": "Asset Details", "ticketDetails": "Ticket Details", "assignTicket": "Assign <PERSON>", "userDetails": "User Details", "itAssetManagement": "IT Asset Management", "role": "Role", "unknown": "Unknown", "user": "User", "assetOverview": "Asset Overview", "totalAssets": "Total Assets", "retired": "Retired", "maintenance": "Maintenance", "unassigned": "Unassigned", "assigned": "Assigned", "userOverview": "User Overview", "totalUsers": "Total Users", "administrators": "Admin", "regularUsers": "Users", "assetDistribution": "Asset Distribution", "quickActions": "Quick Actions", "viewAssets": "View Assets", "addAsset": "Add <PERSON>set", "viewActivityLog": "View Activity Log", "noData": "No Data Available", "confirmLogout": "Confirm <PERSON>ut", "confirmLogoutMessage": "Are you sure you want to logout?", "cancel": "Cancel", "logout": "Logout", "logoutTooltip": "Logout", "statusDistribution": "Status Distribution", "categoryDistribution": "Category Distribution", "myWorkspace": "My Workspace", "myAssets": "My Assets", "createTicket": "Create Ticket", "userProfile": "User Profile", "workspace": "Workspace", "confirmBatchDelete": "Confirm Batch Delete", "confirmBatchDeleteMessage": "Are you sure you want to delete the selected {count} assets?\nThis action cannot be undone.", "@confirmBatchDeleteMessage": {"placeholders": {"count": {"type": "int"}}}, "delete": "Delete", "deletingAssets": "Deleting {count} assets...", "@deletingAssets": {"placeholders": {"count": {"type": "int"}}}, "batchDeleteSuccess": "Batch Delete Successful", "batchDeleteSuccessMessage": "Successfully deleted {count} assets", "@batchDeleteSuccessMessage": {"placeholders": {"count": {"type": "int"}}}, "deleteCompleted": "Delete completed: {successCount} successful, {failCount} failed", "@deleteCompleted": {"placeholders": {"successCount": {"type": "int"}, "failCount": {"type": "int"}}}, "clear": "Clear", "assetCategory": "Asset Category", "allCategories": "All Categories", "assetStatus": "Asset Status", "allStatuses": "All Statuses", "searchAssetsHint": "Search asset name, number or description...", "filter": "Filter", "batchSelect": "Batch Select", "selectedItems": "{count} items selected", "@selectedItems": {"placeholders": {"count": {"type": "int"}}}, "pleaseSelectAssets": "Please select assets", "selectAll": "Select All", "deselectAll": "Deselect All", "deleteSelected": "Delete Selected", "loadingAssets": "Loading asset data...", "categoryMobile": "Mobile Phone", "statusUnassigned": "Unassigned", "confirmDeleteAsset": "Are you sure you want to delete asset \"{name}\"? This action cannot be undone.", "@confirmDeleteAsset": {"placeholders": {"name": {"type": "String"}}}, "assetDeletedSuccess": "Asset deleted successfully", "@deleteFailed": {"placeholders": {"error": {"type": "String"}}}, "notSet": "Not Set", "assetNotExists": "Asset does not exist", "assetNumber": "Number", "category": "Category", "brand": "Brand", "model": "Model", "serialNumber": "Serial Number", "financialInfo": "Financial Information", "value": "Value", "purchaseDate": "Purchase Date", "vendor": "<PERSON><PERSON><PERSON>", "assignmentInfo": "Assignment Information", "assignedTo": "Assigned to: {name}", "location": "Location", "maintenanceInfo": "Maintenance Information", "lastMaintenanceDate": "Last Maintenance Date", "nextMaintenanceDate": "Next Maintenance Date", "systemInfo": "System Information", "createdAtLabel": "Created At", "updatedAtLabel": "Updated At", "description": "Description", "loadingAssetInfo": "Loading asset information...", "loadUserListFailed": "Failed to load user list: {error}", "@loadUserListFailed": {"placeholders": {"error": {"type": "String"}}}, "assetNotFound": "Asset not found", "loadAssetFailed": "Failed to load asset: {error}", "@loadAssetFailed": {"placeholders": {"error": {"type": "String"}}}, "assetUpdatedSuccess": "Asset information updated successfully", "assetCreatedSuccess": "New asset created successfully", "updateAssetFailed": "Failed to update asset", "createAssetFailed": "Failed to create asset", "@operationFailed": {"placeholders": {"error": {"type": "String"}}}, "assetName": "Asset Name", "assetNameHint": "Please enter asset name", "assetNumberHint": "Auto-generated by system", "status": "Status", "brandHint": "Please enter brand name", "modelHint": "Please enter model", "serialNumberHint": "Please enter serial number", "valueHint": "Please enter asset value", "vendorHint": "Please enter vendor name", "assignedUserHint": "Select assigned user", "locationHint": "Please enter location information", "descriptionHint": "Please enter asset description", "save": "Save", "saving": "Saving...", "pleaseEnterAssetName": "Please enter asset name", "pleaseEnterBrand": "Please enter brand", "pleaseEnterModel": "Please enter model", "pleaseEnterSerialNumber": "Please enter serial number", "pleaseEnterValidValue": "Please enter valid value", "pleaseSelectPurchaseDate": "Please select purchase date", "selectDate": "Select date", "pleaseEnter": "Please enter", "pleaseEnterVendor": "Please enter vendor", "pleaseEnterLocation": "Please enter location", "detailedInfo": "Detailed Information", "financialDetails": "Financial Details", "assignmentDetails": "Assignment Details", "maintenanceDetails": "Maintenance Details", "recordCount": "{count} records found", "@recordCount": {"placeholders": {"count": {"type": "int"}}}, "noAssetsFound": "No matching assets found", "noAssetsData": "No asset data available", "adjustSearchCriteria": "Try adjusting search criteria", "addFirstAsset": "Click the button above to add your first asset", "@exportFailed": {"placeholders": {"error": {"type": "String"}}}, "edit": "Edit", "initializing": "Initializing...", "ticketProgress": "Ticket Progress", "problemDescription": "Problem Description", "commentRecords": "Comment Records", "adminOperations": "Admin Operations", "currentAssignee": "Current Assignee", "notAssigned": "Not Assigned", "manualAssignToOtherAdmin": "Manually Assign to Other Administrator", "statusPending": "Pending", "statusInProgress": "In Progress", "statusResolved": "Resolved", "statusClosed": "Closed", "priorityLow": "Low", "priorityMedium": "Medium", "priorityHigh": "High", "categoryHardware": "Hardware", "categorySoftware": "Software", "categoryAccount": "Account", "iWillHandle": "I'll Handle This", "iHaveResolved": "I Have Resolved", "iWillClose": "I'll Close This", "assignTicketDialog": "Assign <PERSON>", "selectAdminToAssign": "Select administrator to assign:", "ticketAssigned": "Ticket has been assigned", "assignmentFailed": "Assignment failed: {error}", "@assignmentFailed": {"placeholders": {"error": {"type": "String"}}}, "statusUpdated": "Status Updated", "ticketAutoAssignedToYou": "Ticket has been automatically assigned to you", "statusUpdateFailed": "Status update failed", "closedTicketCannotChangeStatus": "Closed tickets cannot change status", "rollbackChanceUsed": "Rollback chance has been used, cannot rollback again", "currentStatusNotAllowClose": "Current status does not allow closing", "loginExpiredPleaseRelogin": "<PERSON><PERSON> has expired, please log in again", "networkFailedCheckAndRetry": "Network connection failed, please check network and retry", "addReply": "Add Reply", "enterReplyContent": "Enter reply content...", "sendReply": "Send Reply", "replyHasBeenSent": "Reply has been sent", "sendFailed": "Send failed: {error}", "@sendFailed": {"placeholders": {"error": {"type": "String"}}}, "manageMyComments": "Manage My Comments", "commentDeleted": "Comment deleted", "batchDeletedComments": "Deleted {count} comments in total", "@batchDeletedComments": {"placeholders": {"count": {"type": "int"}}}, "batchDeleteFailed": "Batch delete failed: {error}", "@batchDeleteFailed": {"placeholders": {"error": {"type": "String"}}}, "justNow": "Just now", "minutesAgo": "{minutes} minutes ago", "@minutesAgo": {"placeholders": {"minutes": {"type": "int"}}}, "hoursAgo": "{hours} hours ago", "@hoursAgo": {"placeholders": {"hours": {"type": "int"}}}, "daysAgo": "{days} days ago", "@daysAgo": {"placeholders": {"days": {"type": "int"}}}, "weeksAgo": "{weeks} weeks ago", "@weeksAgo": {"placeholders": {"weeks": {"type": "int"}}}, "monthsAgo": "{months} months ago", "@monthsAgo": {"placeholders": {"months": {"type": "int"}}}, "yearsAgo": "{years} years ago", "@yearsAgo": {"placeholders": {"years": {"type": "int"}}}, "confirmCloseTicket": "Confirm Close Ticket", "closeTicketWarning": "Are you sure you want to close this ticket? Once closed, the ticket cannot be reopened.", "confirmRollback": "Confirm Rollback", "rollbackWarning": "Are you sure you want to rollback this ticket from resolved to in-progress status?", "rollbackTimeLimit": "This rollback must be completed within {hours} hours.", "@rollbackTimeLimit": {"placeholders": {"hours": {"type": "int"}}}, "confirm": "Confirm", "assignmentSuccessful": "Assignment Successful", "ticketAssignedTo": "Ticket has been assigned to {name}", "@ticketAssignedTo": {"placeholders": {"name": {"type": "String"}}}, "noComments": "No comments yet", "showAllComments": "Show all {count} comments", "@showAllComments": {"placeholders": {"count": {"type": "int"}}}, "collapseComments": "Collapse Comments", "me": "Me", "assignedTime": "Assigned Time", "resolvedTime": "Resolved Time", "ticketNotExists": "Ticket does not exist", "loadingFailed": "Loading failed", "getUserInfoFailed": "Failed to get user information", "ticketStatus": "Ticket Status", "priority": "Priority", "ticketCategory": "Ticket Category", "allPriorities": "All Priorities", "clearAll": "Clear All", "searchTicketHint": "Search ticket number, title, username...", "noTickets": "No tickets", "noTicketsUnderCurrentFilter": "No tickets found under current filter conditions", "@assignedTo": {"placeholders": {"name": {"type": "String"}}}, "assignedToLabel": "Assigned To", "commentsCount": "{count} comments", "@commentsCount": {"placeholders": {"count": {"type": "int"}}}, "submittedBy": "Submitted By", "closeConfirmation": "Closing ticket requires confirmation", "rollbackConfirmation": "Rollback operation requires confirmation", "directStatusUpdate": "Other statuses update directly", "autoAssignToCurrentAdmin": "Update ticket status and automatically assign to current administrator", "rollbackInfo": "Rollback Information", "rollbackCountdown": "Rollback Countdown", "remainingRollbackChances": "Remaining rollback chances: {count}", "@remainingRollbackChances": {"placeholders": {"count": {"type": "int"}}}, "rollbackTimeExpired": "Rollback time has expired", "rollbackTimeExpiredWarning": "⚠️ Time is about to expire, please act quickly!", "rollbackDescription": "Current ticket status is \"Resolved\", you can rollback once to \"In Progress\" status."}